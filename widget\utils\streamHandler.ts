/**
 * Widget专用的SSE流式响应处理工具
 * 参考 src/utils/streamResponseManager.ts 的实现
 */

// SSE消息处理回调类型
export interface StreamCallbacks {
  onContent?: (content: string) => void
  onError?: (error: string) => void
  onComplete?: (finalContent: string) => void
  onStart?: () => void
  onReasoningContent?: (reasoningContent: string) => void
}

// 流式响应处理器
export class WidgetStreamHandler {
  private controller: AbortController | null = null
  private reader: ReadableStreamDefaultReader<Uint8Array> | null = null

  /**
   * 处理SSE流式响应
   * @param response - fetch响应对象
   * @param callbacks - 回调函数集合
   */
  async handleStream(response: Response, callbacks: StreamCallbacks): Promise<void> {
    if (!response.body) {
      callbacks.onError?.('响应体为空')
      return
    }

    this.controller = new AbortController()
    this.reader = response.body.getReader()
    const decoder = new TextDecoder()

    let accumulatedContent = ''
    let accumulatedReasoningContent = ''
    let sseBuffer = ''
    let hasError = false
    let isDone = false

    try {
      callbacks.onStart?.()

      while (true) {
        const { done, value } = await this.reader.read()

        if (done) break

        const chunk = decoder.decode(value, { stream: true })

        // 检查错误响应
        if (chunk.includes('"code":500') || chunk.includes('"code": 500')) {
          try {
            const errorData = JSON.parse(chunk)
            if (errorData.code !== 200) {
              callbacks.onError?.(errorData.msg || '请求异常')
              return
            }
          } catch (e) {
            const errorMatch = chunk.match(/"msg"\s*:\s*"([^"]+)"/)
            const errorMsg = errorMatch ? errorMatch[1] : '服务器内部错误'
            callbacks.onError?.(errorMsg)
            return
          }
        }

        // 检查401未授权
        if (chunk.includes('"code":401') || chunk.includes('"code": 401')) {
          callbacks.onError?.('未授权，请重新登录')
          return
        }

        // 将新的chunk数据添加到缓冲区
        sseBuffer += chunk

        // 解析SSE数据
        const { messages, remainingBuffer } = this.parseSSEData(sseBuffer)
        sseBuffer = remainingBuffer

        // 处理每个完整的SSE消息
        for (const message of messages) {
          const lines = message.split('\n')

          for (const line of lines) {
            if (line.startsWith('event:')) {
              const event = line.substring(6).trim()

              // 检测错误事件
              if (event === 'error') {
                hasError = true
                continue
              }

              // 检测DONE事件
              if (event === '[DONE]') {
                isDone = true
                continue
              }

              // 检测CLOSE事件（用户取消）
              if (event === '[CLOSE]') {
                this.cleanup()
                callbacks.onComplete?.(accumulatedContent)
                return
              }
            }

            if (line.startsWith('data:')) {
              const dataContent = line.substring(5).trim()
              if (!dataContent) continue

              try {
                // 如果是错误事件，dataContent 就是错误消息
                if (hasError) {
                  callbacks.onError?.(dataContent || '请求异常')
                  return
                }

                const data = JSON.parse(dataContent)

                // 如果已经收到DONE事件，处理最终内容
                if (isDone) {
                  if (data.content) {
                    accumulatedContent = data.content
                  }
                  callbacks.onComplete?.(accumulatedContent)
                  return
                }

                // 处理内容更新
                if (data.content !== undefined || data.reasoningContent) {
                  // 处理普通内容
                  if (data.content !== undefined) {
                    accumulatedContent += data.content
                  }

                  // 处理DeepSeek R1格式的推理内容
                  if (data.reasoningContent) {
                    accumulatedReasoningContent += data.reasoningContent
                  }

                  // 如果没有 DeepSeek R1 格式的推理内容，则处理 OpenAI 格式的 <think> 标签
                  if (!data.reasoningContent) {
                    // 提取思维链内容（OpenAI格式的兼容处理，支持流式传输中的不完整块）
                    const newThinkingChains = this.extractThinkingChain(accumulatedContent)
                    if (newThinkingChains.length > 0) {
                      // 使用提取到的思维链内容（可能是完整内容或占位符）
                      accumulatedReasoningContent = newThinkingChains
                        .map(chain => chain.content)
                        .join('\n')
                    }
                  }

                  // 通知推理内容更新
                  if (accumulatedReasoningContent) {
                    callbacks.onReasoningContent?.(accumulatedReasoningContent)
                  }

                  // 清理思维链标记
                  const cleanContent = this.cleanThinkingMarkers(accumulatedContent)
                  console.log('流式内容更新:', `${cleanContent.substring(0, 100)}...`)
                  callbacks.onContent?.(cleanContent)
                }
              } catch (e) {
                console.error('解析SSE数据失败:', e, 'data:', dataContent)
              }
            }
          }
        }
      }

      // 流结束，返回最终内容
      callbacks.onComplete?.(accumulatedContent)
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('流式响应被取消')
      } else {
        console.error('流式响应处理错误:', error)
        callbacks.onError?.(error.message || '流式响应处理失败')
      }
    } finally {
      this.cleanup()
    }
  }

  /**
   * 解析SSE数据流
   * 正确处理跨chunk的SSE消息
   */
  private parseSSEData(buffer: string): { messages: string[]; remainingBuffer: string } {
    const messages: string[] = []
    const lines = buffer.split('\n')
    let currentMessage = ''
    let i = 0

    for (const line of lines) {
      i++
      // 如果是最后一行且不以换行符结尾，可能是不完整的数据
      if (i === lines.length && !buffer.endsWith('\n')) {
        return { messages, remainingBuffer: line }
      }

      // 空行表示一个SSE消息的结束
      if (line.trim() === '') {
        if (currentMessage.trim()) {
          messages.push(currentMessage.trim())
          currentMessage = ''
        }
      } else {
        currentMessage += (currentMessage ? '\n' : '') + line
      }
    }

    return { messages, remainingBuffer: '' }
  }

  /**
   * 提取并处理文本中的思维链内容
   * 支持两种格式的思维链：
   * 1. 代码块格式: ```thinking\n...\n```
   * 2. 标签格式: <think>...</think>
   * 3. DeepSeek R1格式: 通过reasoningContent字段输出推理
   *
   * 优化：支持流式传输中的不完整思考块
   */
  private extractThinkingChain(value: string) {
    const newThinkingChains: { content: string }[] = []

    // 处理代码块格式
    if (value.includes('```thinking')) {
      const parts = value.split('```')
      let hasCompleteThinkingBlock = false

      // 首先处理完整的 ```thinking...``` 块
      parts.forEach((part, index) => {
        if (part.startsWith('thinking\n') && index % 2 === 1) {
          const content = part.replace('thinking\n', '').trim()
          if (content) {
            newThinkingChains.push({ content })
            hasCompleteThinkingBlock = true
          }
        }
      })

      // 如果没有完整的thinking块，处理不完整的情况
      if (!hasCompleteThinkingBlock) {
        const thinkingIndex = value.indexOf('```thinking\n')
        if (thinkingIndex !== -1) {
          const contentAfterThinking = value.substring(thinkingIndex + 12) // 12 是 '```thinking\n' 的长度
          if (contentAfterThinking.trim()) {
            newThinkingChains.push({ content: contentAfterThinking.trim() })
          } else {
            // 如果 ```thinking 后面还没有内容，显示占位符
            newThinkingChains.push({ content: '思考中...' })
          }
        }
      }
    }

    // 处理标签格式
    if (value.includes('<think>')) {
      let hasCompleteThinkBlock = false

      // 首先处理完整的 <think>...</think> 块
      if (value.includes('</think>')) {
        const regex = /<think>([\s\S]*?)<\/think>/g
        let match = regex.exec(value)
        while (match !== null) {
          const thinkingContent = match[1].trim()
          if (thinkingContent) {
            newThinkingChains.push({ content: thinkingContent })
            hasCompleteThinkBlock = true
          }
          match = regex.exec(value)
        }
      }

      // 如果没有完整的think块，处理不完整的情况
      if (!hasCompleteThinkBlock) {
        // 直接提取 <think> 后的内容，即使还没有 </think>
        const thinkStartIndex = value.indexOf('<think>')
        if (thinkStartIndex !== -1) {
          const contentAfterThink = value.substring(thinkStartIndex + 7) // 7 是 '<think>' 的长度
          if (contentAfterThink.trim()) {
            newThinkingChains.push({ content: contentAfterThink.trim() })
          } else {
            // 如果 <think> 后面还没有内容，显示占位符
            newThinkingChains.push({ content: '思考中...' })
          }
        }
      }
    }

    return newThinkingChains
  }

  /**
   * 清理思维链标记
   * 移除 <think>...</think> 和 ```thinking...``` 格式的内容
   */
  private cleanThinkingMarkers(value: string): string {
    let processedValue = value

    // 移除代码块格式的思维链
    if (processedValue.includes('```thinking')) {
      // 先移除完整的 ```thinking...``` 块
      const parts = processedValue.split('```')
      processedValue = parts
        .filter((part, index) => {
          return !(index % 2 === 1 && part.startsWith('thinking\n'))
        })
        .join('')

      // 处理不完整的 ```thinking 块
      if (
        processedValue.includes('```thinking') &&
        !processedValue.includes('```', processedValue.indexOf('```thinking') + 12)
      ) {
        const thinkingIndex = processedValue.indexOf('```thinking')
        processedValue = processedValue.substring(0, thinkingIndex)
      }
    }

    // 移除标签格式的思维链
    if (processedValue.includes('<think>')) {
      // 先移除完整的 <think>...</think> 块
      processedValue = processedValue.replace(/<think>[\s\S]*?<\/think>/g, '')

      // 处理不完整的 <think> 块
      if (processedValue.includes('<think>') && !processedValue.includes('</think>')) {
        const thinkIndex = processedValue.indexOf('<think>')
        processedValue = processedValue.substring(0, thinkIndex)
      }
    }

    return processedValue
  }

  /**
   * 停止流式响应
   */
  stop(): void {
    if (this.controller) {
      this.controller.abort()
    }
    this.cleanup()
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    if (this.reader) {
      this.reader.cancel().catch(() => {})
      this.reader = null
    }
    this.controller = null
  }
}

// 创建单例实例
export const widgetStreamHandler = new WidgetStreamHandler()
