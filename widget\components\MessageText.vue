<script lang="ts" setup>
import { computed, onMounted, onUnmounted, onUpdated, ref, watch } from 'vue'
import MarkdownIt from 'markdown-it'
import MdKatex from '@vscode/markdown-it-katex'
import MdLinkAttributes from 'markdown-it-link-attributes'
import hljs from 'highlight.js'
// 导入KaTeX CSS样式
import 'katex/dist/katex.min.css'
// 导入highlight.js CSS样式
import 'highlight.js/styles/github.css'

interface Props {
  inversion?: boolean
  error?: boolean
  text?: string
  loading?: boolean
  asRawText?: boolean
  reasoningContent?: string
}

const props = defineProps<Props>()

// Refs
const textRef = ref<HTMLElement>()
const displayedText = ref('')
const fullText = ref('')
const isTyping = ref(false)

// 思维链相关状态
const reasoningCollapseValue = ref<string[]>([])
const hasAutoCollapsed = ref(false) // 标记是否已经自动闭合过
const thinkingCopyStatus = ref<{ [key: number]: boolean }>({})
const isThinkingExpanded = ref(false)

// 思维链打字机效果状态
const displayedReasoningContent = ref('')
const fullReasoningContent = ref('')
const isReasoningTyping = ref(false)
let reasoningTypingTimer: number | null = null

// Typing effect variables
const typingSpeed = 15
const typingBatchSize = 3
let typingTimer: number | null = null

// Markdown-it configuration
const mdBaseConfig = {
  html: false,
  linkify: true,
}

const mdi = new MarkdownIt({
  ...mdBaseConfig,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

// 思维链专用的markdown处理器
const thinkingMdi = new MarkdownIt({
  ...mdBaseConfig,
  highlight: code => code,
})
  .disable('code')
  .disable('fence')

// Add plugins
mdi.use(MdLinkAttributes, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(MdKatex, {
  throwOnError: false,
  errorColor: '#cc0000',
  strict: false,
})

thinkingMdi.use(MdLinkAttributes, { attrs: { target: '_blank', rel: 'noopener' } })
thinkingMdi.use(MdKatex, {
  throwOnError: false,
  errorColor: '#cc0000',
  strict: false,
})

/**
 * 渲染 Markdown 文本为 HTML
 */
const renderMarkdown = (text: string) => {
  console.log('Rendering markdown text:', text)
  const result = mdi.render(text)
  console.log('Rendered result:', result)
  return result
}

/**
 * 处理文本内容并转换为 HTML
 */
const processTextToHtml = (value: string, asRawText: boolean) => {
  if (asRawText) return value
  return renderMarkdown(value)
}

const html = computed(() => {
  const value = props.text ?? ''
  console.log('html computed - props:', {
    inversion: props.inversion,
    asRawText: props.asRawText,
    loading: props.loading,
  })
  console.log('html computed - value:', value)

  if (props.inversion || props.asRawText) {
    console.log('Returning raw value due to inversion or asRawText')
    return value
  }
  const textToRender =
    !isTyping.value || props.loading === false ? value : displayedText.value || value
  console.log('Processing text through markdown:', textToRender)
  return processTextToHtml(textToRender, false)
})

/**
 * 思维链内容的HTML渲染（支持打字机效果）
 */
const reasoningHtml = computed(() => {
  const reasoningContent = props.reasoningContent || ''
  if (!reasoningContent) return ''

  const contentToRender =
    !isReasoningTyping.value || props.loading === false
      ? reasoningContent
      : displayedReasoningContent.value || reasoningContent

  return thinkingMdi.render(contentToRender)
})

/**
 * 生成代码块的 HTML，包含语言标识和复制按钮
 */
function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy cursor-pointer">复制代码</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

// Computed properties
const wrapClass = computed(() => {
  return ['widget-message-text', { 'widget-message-error': props.error }]
})

/**
 * 实现打字机效果
 */
function startTypewriterEffect(startText: string, targetText: string) {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  isTyping.value = true
  displayedText.value = startText
  if (displayedText.value === targetText) {
    isTyping.value = false
    return
  }
  let currentIndex = displayedText.value.length
  const typeNextChar = () => {
    if (currentIndex < targetText.length) {
      const endIndex = Math.min(currentIndex + typingBatchSize, targetText.length)
      displayedText.value = targetText.substring(0, endIndex)
      currentIndex = endIndex
      const hasSpecialChars = /[`<|]/.test(
        targetText.substring(Math.max(0, currentIndex - 20), currentIndex),
      )
      typingTimer = window.setTimeout(typeNextChar, hasSpecialChars ? typingSpeed / 2 : typingSpeed)
    } else {
      isTyping.value = false
    }
  }
  typingTimer = window.setTimeout(typeNextChar, typingSpeed)
}

/**
 * 清理资源
 */
function cleanup() {
  if (typingTimer) {
    clearTimeout(typingTimer)
    typingTimer = null
  }
  if (reasoningTypingTimer) {
    clearTimeout(reasoningTypingTimer)
    reasoningTypingTimer = null
  }
}

/**
 * 启动思维链打字机效果
 */
function startReasoningTypewriterEffect(startText: string, targetText: string) {
  if (reasoningTypingTimer) {
    clearTimeout(reasoningTypingTimer)
    reasoningTypingTimer = null
  }

  fullReasoningContent.value = targetText
  displayedReasoningContent.value = startText
  isReasoningTyping.value = true

  const typeReasoningChar = () => {
    const current = displayedReasoningContent.value
    const target = fullReasoningContent.value

    if (current.length < target.length) {
      const nextLength = Math.min(current.length + typingBatchSize, target.length)
      displayedReasoningContent.value = target.substring(0, nextLength)
      reasoningTypingTimer = window.setTimeout(typeReasoningChar, typingSpeed)
    } else {
      isReasoningTyping.value = false
      reasoningTypingTimer = null
    }
  }

  reasoningTypingTimer = window.setTimeout(typeReasoningChar, typingSpeed)
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    const success = document.execCommand('copy')
    document.body.removeChild(textArea)
    return success
  }
}

/**
 * 为代码块的复制按钮添加事件监听
 */
function addCopyEvents() {
  if (textRef.value) {
    const copyBtn = textRef.value.querySelectorAll('.code-block-header__copy')
    copyBtn.forEach(btn => {
      btn.addEventListener('click', async () => {
        const code = btn.parentElement?.nextElementSibling?.textContent
        if (code) {
          const success = await copyToClipboard(code)
          if (success) {
            btn.textContent = '已复制'
            setTimeout(() => {
              btn.textContent = '复制代码'
            }, 1000)
          }
        }
      })
    })
  }
}

/**
 * 复制思维链内容
 * 复制成功后会更新复制按钮状态
 */
const copyThinkingContent = async (content: string, index: number) => {
  const success = await copyToClipboard(content)
  if (success) {
    thinkingCopyStatus.value = { ...thinkingCopyStatus.value, [index]: true }
    setTimeout(() => {
      thinkingCopyStatus.value = { ...thinkingCopyStatus.value, [index]: false }
    }, 1000)
  }
}

/**
 * 切换思维链展开状态
 */
const toggleThinking = () => {
  isThinkingExpanded.value = !isThinkingExpanded.value
}

watch(
  () => props.text,
  (newText, oldText) => {
    const newTextValue = newText || ''
    const oldTextValue = oldText || ''

    if (newTextValue && !props.inversion) {
      // AI消息启用打字机效果
      if (props.loading && newTextValue !== oldTextValue) {
        // 流式更新时，启用打字机效果
        fullText.value = newTextValue
        if (newTextValue.length > oldTextValue.length) {
          // 文本增加时，从当前显示的文本开始打字
          startTypewriterEffect(displayedText.value, newTextValue)
        } else {
          // 文本完全替换时，从头开始打字
          startTypewriterEffect('', newTextValue)
        }
      } else {
        // 非流式更新，直接显示
        displayedText.value = newTextValue
        fullText.value = newTextValue
        isTyping.value = false
      }
    } else {
      // 用户消息直接显示
      displayedText.value = newTextValue
      fullText.value = newTextValue
      isTyping.value = false
    }
  },
  { immediate: true },
)

watch(
  () => props.loading,
  newLoading => {
    if (newLoading === false && fullText.value) {
      displayedText.value = fullText.value
      isTyping.value = false
      if (typingTimer) {
        clearTimeout(typingTimer)
        typingTimer = null
      }
    }
  },
  { immediate: true },
)

// 监听推理内容变化，实现展开/折叠逻辑和打字机效果
watch(
  () => props.reasoningContent,
  (newContent, oldContent) => {
    const newContentValue = newContent || ''
    const oldContentValue = oldContent || ''

    // 只有在加载状态（实时推理）时才展开，历史记录不展开
    if (newContentValue && !oldContentValue && props.loading) {
      isThinkingExpanded.value = true
      hasAutoCollapsed.value = false // 重置自动闭合标记
    }

    // 实现思维链的打字机效果
    if (newContentValue && props.loading) {
      if (newContentValue !== oldContentValue) {
        if (newContentValue.length > oldContentValue.length) {
          // 内容增加时，从当前显示的内容开始打字
          startReasoningTypewriterEffect(displayedReasoningContent.value, newContentValue)
        } else {
          // 内容完全替换时，从头开始打字
          startReasoningTypewriterEffect('', newContentValue)
        }
      }
    } else {
      // 非流式更新，直接显示
      displayedReasoningContent.value = newContentValue
      fullReasoningContent.value = newContentValue
      isReasoningTyping.value = false
    }
  },
  { immediate: true },
)

// 监听正文内容变化，当开始接收正文时自动闭合推理区域
watch(
  () => props.text,
  (newText, oldText) => {
    // 有正文内容且还没有自动闭合过，则自动闭合
    if (newText && !hasAutoCollapsed.value && props.reasoningContent) {
      isThinkingExpanded.value = false
      hasAutoCollapsed.value = true
    }
  },
)

// Lifecycle hooks
onMounted(() => {
  addCopyEvents()
})

onUpdated(() => {
  addCopyEvents()
})

onUnmounted(() => {
  cleanup()
})
</script>

<template>
  <div :class="wrapClass">
    <div ref="textRef" class="widget-message-content">
      <!-- 用户消息 -->
      <div v-if="props.inversion" class="widget-user-text" v-text="html" />
      <!-- AI回复 -->
      <div v-else class="widget-ai-text">
        <!-- 显示思维链内容 -->
        <div
          v-if="props.reasoningContent && props.reasoningContent.length > 0"
          class="thinking-container"
        >
          <div class="thinking-header" @click="toggleThinking">
            <div class="thinking-title">
              <span class="thinking-name">已深度思考</span>
            </div>
            <div class="thinking-actions">
              <button
                class="thinking-copy-btn"
                :class="{ copied: thinkingCopyStatus[0] }"
                @click.stop="copyThinkingContent(props.reasoningContent, 0)"
              >
                {{ thinkingCopyStatus[0] ? '✓' : '📋' }}
              </button>
              <span class="thinking-arrow" :class="{ expanded: isThinkingExpanded }">▼</span>
            </div>
          </div>
          <div v-if="isThinkingExpanded" class="thinking-content">
            <div
              class="thinking-text"
              :class="{ 'thinking-text-generate': isReasoningTyping }"
              v-html="reasoningHtml"
            ></div>
          </div>
        </div>

        <!-- 显示typing-indicator当loading且没有文本时 -->
        <div v-if="props.loading && !props.text" class="typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
        <!-- 正常文本内容 -->
        <div v-if="props.text">
          <div
            v-if="!props.asRawText"
            class="markdown-body"
            :class="{ 'markdown-body-generate': props.loading }"
            v-html="html"
          />
          <div v-else class="whitespace-pre-wrap" v-text="html" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.widget-message-text {
  width: 100%;
}

.widget-message-content {
  word-wrap: break-word;
  word-break: break-word;
  line-height: 1.5;
}

.widget-user-text {
  white-space: pre-wrap;
  font-size: 14px;
}

.widget-ai-text {
  font-size: 14px;
}

.widget-message-error {
  color: #ef4444;
}

/* Markdown 样式 */
:deep(.markdown-body) {
  font-size: 14px;
  line-height: 1.6;
}

:deep(.markdown-body h1),
:deep(.markdown-body h2),
:deep(.markdown-body h3),
:deep(.markdown-body h4),
:deep(.markdown-body h5),
:deep(.markdown-body h6) {
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  font-weight: 600;
}

:deep(.markdown-body p) {
  margin-bottom: 0.5em;
  margin-top: 0.5em;
  line-height: 1.6;
}

:deep(.markdown-body ul),
:deep(.markdown-body ol) {
  margin-bottom: 0.6em;
  padding-left: 1.5em;
}

:deep(.markdown-body li) {
  margin-bottom: 0.2em;
  line-height: 1.6;
}

:deep(.markdown-body blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1em;
  margin: 0.6em 0;
  color: #6b7280;
  line-height: 1.6;
}

:deep(.markdown-body table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

:deep(.markdown-body th),
:deep(.markdown-body td) {
  border: 1px solid #e5e7eb;
  padding: 0.5em;
  text-align: left;
}

:deep(.markdown-body th) {
  background-color: #f9fafb;
  font-weight: 600;
}

/* 代码块样式 */
:deep(.code-block-wrapper) {
  margin: 1em 0;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

:deep(.code-block-header) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em 1em;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
}

:deep(.code-block-header__lang) {
  color: #6b7280;
  font-weight: 500;
}

:deep(.code-block-header__copy) {
  color: #4c5cec;
  cursor: pointer;
  font-size: 12px;
}

:deep(.code-block-header__copy:hover) {
  color: #3730a3;
}

:deep(.code-block-body) {
  display: block;
  padding: 1em;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

/* 行内代码样式 */
:deep(.markdown-body code:not(.hljs)) {
  background-color: #f1f5f9;
  padding: 0.125em 0.25em;
  border-radius: 3px;
  font-size: 0.875em;
  font-family: 'Courier New', monospace;
}

/* 链接样式 */
:deep(.markdown-body a) {
  color: #4c5cec;
  text-decoration: none;
}

:deep(.markdown-body a:hover) {
  text-decoration: underline;
}

/* 加载状态动画 */
.markdown-body-generate {
  position: relative;
}

.markdown-body-generate::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #999;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* Typing indicator styles */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;

  .typing-dots {
    display: flex;
    gap: 3px;
    padding: 8px 12px;
    background: white;
    border-radius: 12px;
    /* box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); */

    :is(span) {
      width: 6px;
      height: 6px;
      background: #999;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
      &:nth-child(3) {
        animation-delay: 0s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 思维链样式 */
.thinking-container {
  margin-bottom: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.thinking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid #e5e7eb;
}

.thinking-header:hover {
  background: #f3f4f6;
}

.thinking-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.thinking-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.thinking-copy-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  color: #6b7280;
  transition: all 0.2s;
}

.thinking-copy-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.thinking-copy-btn.copied {
  color: #10b981;
}

.thinking-arrow {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.2s;
}

.thinking-arrow.expanded {
  transform: rotate(180deg);
}

.thinking-content {
  padding: 16px;
  background: white;
  border-radius: 0 0 8px 8px;
  max-height: 300px;
  overflow-y: auto;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.2s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 移除滚动条箭头 */
  &::-webkit-scrollbar-button {
    display: none;
  }

  /* Firefox 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.thinking-text {
  font-size: 13px;
  line-height: 1.5;
  color: #374151;
  white-space: pre-wrap;
}

/* 思维链打字机效果动画 */
.thinking-text-generate {
  position: relative;
}

.thinking-text-generate::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: #999;
  animation: blink 1s infinite;
  margin-left: 2px;
  vertical-align: text-bottom;
}
</style>
